import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    // User Center Module Routes
    {
      path: '/profile',
      name: 'profile',
      component: () => import('../views/UserProfile.vue'),
      meta: {
        title: '个人资料',
        requiresAuth: true
      }
    },
    {
      path: '/wallet',
      name: 'wallet',
      component: () => import('../views/Wallet.vue'),
      meta: {
        title: '我的钱包',
        requiresAuth: true
      }
    },
    // Patent Management Module Routes
    {
      path: '/patent/upload',
      name: 'patent-upload',
      component: () => import('../views/PatentUpload.vue'),
      meta: {
        title: '专利上传',
        requiresAuth: true
      }
    },
    {
      path: '/patent/search',
      name: 'patent-search',
      component: () => import('../views/PatentSearch.vue'),
      meta: {
        title: '专利搜索',
        requiresAuth: false
      }
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 区块链专利交易平台`
  } else {
    document.title = '区块链专利交易平台'
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 这里可以检查用户是否已连接 MetaMask
    // 暂时允许所有访问，实际项目中应该检查 MetaMask 连接状态
    next()
  } else {
    next()
  }
})

export default router