# Frontend Todo List - Blockchain Patent Exchange System

## 1. MetaMask Integration Module

### 1.1 Wallet Connection
- [x] Create MetaMaskConnector.vue component
- [x] Implement wallet installation detection
- [x] Implement wallet connection functionality
- [x] Add account switching monitoring
- [x] Add network change detection
- [x] Implement Ganache private chain validation
- [x] Create NetworkValidator.vue component
- [x] Add connection status display
- [x] Implement network switching guidance

### 1.2 Authentication Integration
- [x] Integrate with auth check API endpoint
- [x] Implement user registration check by address
- [x] Add user info retrieval functionality

## 2. User Center Module

### 2.1 Personal Information Management
- [x] Create UserProfile.vue component
- [x] Implement profile route `/profile`
- [x] Display blockchain address (read-only)
- [x] Add personal info form (name, phone, ID card)
- [x] Implement first-time user info completion requirement
- [x] Add blockchain transaction confirmation for info updates
- [x] Integrate with profile API endpoints

### 2.2 Wallet Functionality
- [x] Create Wallet.vue main component
- [x] Implement wallet route `/wallet`
- [x] Display wallet balance from smart contract
- [x] Create RechargeModal.vue component
- [x] Create WithdrawModal.vue component
- [x] Create TransactionHistory.vue component
- [x] Implement simulated recharge functionality
- [x] Implement simulated withdrawal functionality
- [x] Display transaction history (income/expense, patent name, time)
- [x] Integrate with wallet API endpoints

## 3. Patent Management Module

### 3.1 Patent Upload
- [ ] Create PatentUpload.vue component
- [ ] Implement upload route `/patent/upload`
- [ ] Add patent basic info form (name, number, category, price, summary, dates)
- [ ] Add patent owner info form (name, ID card)
- [ ] Add agency sale option (radio button)
- [ ] Implement conditional document upload based on agency status
- [ ] Add patent document upload functionality
- [ ] Support multiple file formats (Word, PDF, PNG)
- [ ] Integrate with IPFS upload API
- [ ] Set patent status to "under review" after upload
- [ ] Integrate with patent creation API

### 3.2 Patent Search
- [ ] Create PatentSearch.vue component
- [ ] Create PatentList.vue component
- [ ] Create PatentCard.vue component
- [ ] Implement search route `/patent/search`
- [ ] Add fuzzy and exact search by patent name
- [ ] Add exact search by patent number
- [ ] Add category filtering
- [ ] Implement pagination for search results
- [ ] Display search results with patent info
- [ ] Add click-to-view-details functionality
- [ ] Integrate with search API endpoints

### 3.3 Patent Detail Page
- [ ] Create PatentDetail.vue component
- [ ] Create PatentDocuments.vue component
- [ ] Create PatentEvents.vue component
- [ ] Create TradeButton.vue component
- [ ] Implement detail route `/patent/:id`
- [ ] Display complete patent information
- [ ] Show uploader information with click-to-view functionality
- [ ] Display agency sale status
- [ ] Implement document viewing and download
- [ ] Show trade button for eligible patents
- [ ] Display patent event traceability
- [ ] Integrate with patent detail API

### 3.4 My Patents Management
- [ ] Create MyPatents.vue main component
- [ ] Create PatentStatusControl.vue component
- [ ] Create TradeContractModal.vue component
- [ ] Implement published patents route `/my-patents/published`
- [ ] Implement purchased patents route `/my-patents/purchased`
- [ ] Implement owned patents route `/my-patents/owned`
- [ ] Display published patents with status and management options
- [ ] Add patent revocation functionality
- [ ] Add patent freeze/unfreeze functionality
- [ ] Display purchased patents with transaction details
- [ ] Show trade contract download for completed trades
- [ ] Display currently owned patents with re-listing capability
- [ ] Integrate with my patents API endpoints

## 4. Patent Trading Module

### 4.1 Patent Trading Process
- [ ] Create PatentTrade.vue component
- [ ] Create TradeConfirmation.vue component
- [ ] Implement trade route `/patent/:id/trade`
- [ ] Display trade confirmation info (patent details, price, seller info)
- [ ] Implement wallet balance verification
- [ ] Add trade initiation with smart contract call
- [ ] Set patent status to "trading" after application
- [ ] Handle trade approval workflow
- [ ] Implement automatic ownership and fund transfer
- [ ] Integrate with trading API endpoints

### 4.2 Trade Contract
- [ ] Create TradeContract.vue component
- [ ] Implement automatic contract generation after approval
- [ ] Include complete trade information in contract
- [ ] Add contract download functionality
- [ ] Add contract modal viewing
- [ ] Store contract as permanent trade evidence
- [ ] Integrate with contract API endpoints

## 5. Patent Rights Protection Module

### 5.1 Rights Protection Application
- [ ] Create RightsProtection.vue component
- [ ] Create RightsEvidenceUpload.vue component
- [ ] Implement rights protection route `/patent/:id/rights-protection`
- [ ] Display target patent blockchain address and info
- [ ] Add rights protection description form
- [ ] Implement evidence document upload to IPFS
- [ ] Submit rights protection application
- [ ] Handle approval workflow
- [ ] Implement ownership transfer upon successful protection
- [ ] Integrate with rights protection API

## 6. Audit Management Module

### 6.1 Pending Audit List
- [ ] Create AuditPending.vue main component
- [ ] Create PatentAuditCard.vue component
- [ ] Create TradeAuditCard.vue component
- [ ] Create RightsAuditCard.vue component
- [ ] Create AuditForm.vue component
- [ ] Implement audit route `/audit/pending`
- [ ] Display categorized pending audits (upload, trade, rights protection)
- [ ] Show patent upload audit details with document access
- [ ] Show trade audit details with party information
- [ ] Show rights protection audit with evidence documents
- [ ] Implement approve/reject actions with reason input
- [ ] Integrate with audit API endpoints

### 6.2 Audit History
- [ ] Create AuditHistory.vue component
- [ ] Create AuditRecord.vue component
- [ ] Implement audit history route `/audit/history`
- [ ] Display historical audit records by category
- [ ] Show audit details (object, result, reason, time, auditor)
- [ ] Add filtering by time, type, and result
- [ ] Provide detailed audit log viewing
- [ ] Integrate with audit history API

## 7. Notification System Module

### 7.1 Notification Center
- [ ] Create NotificationCenter.vue component
- [ ] Create NotificationItem.vue component
- [ ] Create NotificationFilter.vue component
- [ ] Implement notifications route `/notifications`
- [ ] Display real-time user notifications
- [ ] Show notification types (audit results, trades, rights protection)
- [ ] Include patent name, event type, time, and related links
- [ ] Implement read/unread status management
- [ ] Add notification deletion and batch operations
- [ ] Integrate with notifications API


## 8. Administrator Module

### 8.1 User Management
- [ ] Create UserManagement.vue component
- [ ] Create UserTable.vue component
- [ ] Create UserDetailModal.vue component
- [ ] Create PermissionEditor.vue component
- [ ] Implement user management route `/admin/users`
- [ ] Display all registered users list
- [ ] Show user info (address, name, phone, ID, registration time)
- [ ] Implement user permission management (user/auditor roles)
- [ ] Add user status management (active/inactive)
- [ ] Display user activity statistics
- [ ] Add user search functionality (name, phone, address)
- [ ] Integrate with user management API

### 8.2 System Configuration Management
- [ ] Create SystemSettings.vue component
- [ ] Create ConfigEditor.vue component
- [ ] Implement settings route `/admin/settings`
- [ ] Add system parameter configuration (transaction fees, timeout)
- [ ] Implement IPFS node configuration management
- [ ] Add blockchain network configuration
- [ ] Implement system maintenance mode toggle
- [ ] Add backup and restore functionality
- [ ] Integrate with settings API
